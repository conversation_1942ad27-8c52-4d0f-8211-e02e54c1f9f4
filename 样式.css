/* 弹出页面样式 */
.popup-container {
  width: 400px;
  min-height: 500px;
  font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #333;
}

.popup-header {
  background: rgba(255, 255, 255, 0.95);
  padding: 15px 20px;
  border-bottom: 2px solid #e0e0e0;
  text-align: center;
}

.popup-header h1 {
  margin: 0;
  font-size: 18px;
  color: #333;
  font-weight: 600;
}

.popup-main {
  padding: 20px;
  background: rgba(255, 255, 255, 0.98);
  min-height: 450px;
}

.request-info {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 20px;
  border-left: 4px solid #007bff;
}

.request-info h2 {
  margin: 0 0 10px 0;
  font-size: 16px;
  color: #495057;
}

.request-info p {
  margin: 5px 0;
  font-size: 14px;
  color: #6c757d;
}

.controls {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.execute-btn, .clear-btn {
  flex: 1;
  padding: 12px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.execute-btn {
  background: linear-gradient(45deg, #28a745, #20c997);
  color: white;
}

.execute-btn:hover:not(:disabled) {
  background: linear-gradient(45deg, #218838, #1ea080);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

.execute-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
  transform: none;
}

.clear-btn {
  background: linear-gradient(45deg, #dc3545, #fd7e14);
  color: white;
}

.clear-btn:hover {
  background: linear-gradient(45deg, #c82333, #e8650e);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
}

.btn-icon {
  font-size: 16px;
}

.status {
  padding: 10px 15px;
  border-radius: 6px;
  margin-bottom: 20px;
  font-size: 14px;
  font-weight: 500;
  text-align: center;
  background: #e9ecef;
  color: #495057;
  border: 1px solid #dee2e6;
}

.status.loading {
  background: #cce5ff;
  color: #0066cc;
  border-color: #99d6ff;
}

.status.success {
  background: #d4edda;
  color: #155724;
  border-color: #c3e6cb;
}

.status.error {
  background: #f8d7da;
  color: #721c24;
  border-color: #f5c6cb;
}

.status.warning {
  background: #fff3cd;
  color: #856404;
  border-color: #ffeaa7;
}

.status.info {
  background: #d1ecf1;
  color: #0c5460;
  border-color: #bee5eb;
}

.results-section h3 {
  margin: 0 0 10px 0;
  font-size: 16px;
  color: #495057;
}

.results-container {
  border: 2px solid #e9ecef;
  border-radius: 8px;
  overflow: hidden;
}

#results {
  width: 100%;
  height: 200px;
  padding: 15px;
  border: none;
  font-family: 'Consolas', 'Monaco', monospace;
  font-size: 12px;
  line-height: 1.4;
  background: #f8f9fa;
  color: #333;
  resize: vertical;
  outline: none;
}

#results:focus {
  background: #fff;
}

/* 浮动按钮样式 */
.temu-floating-btn {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 10000;
  padding: 12px 20px;
  background: linear-gradient(45deg, #007bff, #6610f2);
  color: white;
  border: none;
  border-radius: 25px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
  transition: all 0.3s ease;
  font-family: 'Microsoft YaHei', Arial, sans-serif;
}

.temu-floating-btn:hover:not(:disabled) {
  background: linear-gradient(45deg, #0056b3, #520dc2);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 123, 255, 0.4);
}

.temu-floating-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
  transform: none;
}

/* 通知样式 */
.temu-notification {
  position: fixed;
  top: 80px;
  right: 20px;
  z-index: 10001;
  padding: 12px 20px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  max-width: 300px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  animation: slideIn 0.3s ease;
  font-family: 'Microsoft YaHei', Arial, sans-serif;
}

.temu-notification.success {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.temu-notification.error {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.temu-notification.info {
  background: #d1ecf1;
  color: #0c5460;
  border: 1px solid #bee5eb;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}
