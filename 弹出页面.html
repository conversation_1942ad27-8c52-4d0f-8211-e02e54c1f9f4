<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>全托获取JSON数据</title>
  <link rel="stylesheet" href="样式.css" />
</head>
<body>
  <div class="popup-container">
    <header class="popup-header">
      <h1>全托数据获取工具</h1>
    </header>
    
    <main class="popup-main">
      <div class="request-info">
        <h2>请求信息</h2>
        <p><strong>目标网址:</strong> agentseller.temu.com</p>
        <p><strong>请求方法:</strong> POST</p>
        <p><strong>数据类型:</strong> JSON</p>
      </div>
      
      <div class="controls">
        <button id="executeRequest" class="execute-btn">
          <span class="btn-icon">🚀</span>
          执行请求
        </button>
        
        <button id="clearResults" class="clear-btn">
          <span class="btn-icon">🗑️</span>
          清空结果
        </button>
      </div>
      
      <div class="status" id="status">
        准备就绪
      </div>
      
      <div class="results-section">
        <h3>请求结果</h3>
        <div class="results-container">
          <textarea id="results" readonly placeholder="请求结果将显示在这里..."></textarea>
        </div>
      </div>
    </main>
  </div>
  
  <script src="弹出页面.js"></script>
</body>
</html>
