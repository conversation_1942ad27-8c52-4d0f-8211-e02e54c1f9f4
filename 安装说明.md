# 全托获取JSON数据浏览器插件

## 功能说明
这是一个Chrome浏览器插件，用于在当前页面执行POST请求获取Temu全托订单数据。

## 主要功能
- 点击插件图标打开弹出页面
- 执行POST请求到指定API接口
- 显示JSON格式的响应数据
- 在页面上添加浮动按钮快速执行请求
- 支持清空结果和状态提示

## 安装步骤

### 1. 开启开发者模式
1. 打开Chrome浏览器
2. 在地址栏输入 `chrome://extensions/`
3. 在右上角开启"开发者模式"

### 2. 加载插件
1. 点击"加载已解压的扩展程序"
2. 选择包含插件文件的文件夹
3. 插件将自动加载并显示在扩展程序列表中

### 3. 使用插件
1. 访问Temu相关网站
2. 点击浏览器工具栏中的插件图标
3. 在弹出页面中点击"执行请求"按钮
4. 查看返回的JSON数据

## 文件结构
```
全托获取JSON/
├── manifest.json          # 插件配置文件
├── 弹出页面.html          # 插件弹出界面
├── 弹出页面.js            # 弹出页面逻辑
├── 内容脚本.js            # 网页注入脚本
├── 样式.css               # 样式文件
└── 安装说明.md            # 本说明文件
```

## 请求配置
- **URL**: `https://agentseller.temu.com/mms/venom/api/supplier/purchase/manager/querySubOrderList`
- **方法**: POST
- **数据格式**: JSON
- **请求体**:
```json
{
  "pageNo": 1,
  "pageSize": 100,
  "urgencyType": 1,
  "isCustomGoods": false,
  "statusList": [1],
  "oneDimensionSort": {
    "firstOrderByParam": "expectLatestDeliverTime",
    "firstOrderByDesc": 0
  }
}
```

## 注意事项
1. 插件需要在Temu相关域名下使用才能正常工作
2. 请求需要有效的登录状态和权限
3. 如遇到CORS错误，请确保在正确的域名下使用
4. 插件会在页面右上角添加一个浮动按钮作为快捷方式

## 故障排除
- 如果请求失败，请检查网络连接和登录状态
- 如果插件无法加载，请确认所有文件都在同一文件夹中
- 查看浏览器控制台获取详细错误信息

## 版本信息
- 版本: 1.0
- 兼容性: Chrome 88+
- 开发日期: 2025年
