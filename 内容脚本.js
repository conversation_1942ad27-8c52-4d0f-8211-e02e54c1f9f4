// 内容脚本 - 在网页中执行的脚本
(function() {
  'use strict';

  // 请求配置
  const REQUEST_CONFIG = {
    url: 'https://agentseller.temu.com/mms/venom/api/supplier/purchase/manager/querySubOrderList',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'X-Requested-With': 'XMLHttpRequest'
    },
    data: {
      "pageNo": 1,
      "pageSize": 100,
      "urgencyType": 1,
      "isCustomGoods": false,
      "statusList": [1],
      "oneDimensionSort": {
        "firstOrderByParam": "expectLatestDeliverTime",
        "firstOrderByDesc": 0
      }
    }
  };

  // 监听来自弹出页面的消息
  chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.action === 'executeRequest') {
      executeApiRequest()
        .then(data => {
          sendResponse({success: true, data: data});
        })
        .catch(error => {
          console.error('API请求失败:', error);
          sendResponse({success: false, error: error.message});
        });
      
      // 返回true表示异步响应
      return true;
    }
  });

  // 执行API请求
  async function executeApiRequest() {
    try {
      console.log('开始执行API请求...');
      
      // 使用fetch发送POST请求
      const response = await fetch(REQUEST_CONFIG.url, {
        method: REQUEST_CONFIG.method,
        headers: REQUEST_CONFIG.headers,
        body: JSON.stringify(REQUEST_CONFIG.data),
        credentials: 'include' // 包含cookies
      });

      // 检查响应状态
      if (!response.ok) {
        throw new Error(`HTTP错误! 状态码: ${response.status}`);
      }

      // 解析JSON响应
      const data = await response.json();
      console.log('API请求成功:', data);
      
      return data;
    } catch (error) {
      console.error('执行API请求时出错:', error);
      throw error;
    }
  }

  // 在页面中添加一个浮动按钮（可选）
  function createFloatingButton() {
    // 检查是否已存在按钮
    if (document.getElementById('temu-api-button')) {
      return;
    }

    const button = document.createElement('button');
    button.id = 'temu-api-button';
    button.innerHTML = '🚀 获取数据';
    button.className = 'temu-floating-btn';
    
    // 添加点击事件
    button.addEventListener('click', async function() {
      button.disabled = true;
      button.innerHTML = '⏳ 请求中...';
      
      try {
        const data = await executeApiRequest();
        console.log('浮动按钮请求成功:', data);
        
        // 显示成功消息
        showNotification('请求成功！数据已输出到控制台', 'success');
      } catch (error) {
        console.error('浮动按钮请求失败:', error);
        showNotification('请求失败: ' + error.message, 'error');
      } finally {
        button.disabled = false;
        button.innerHTML = '🚀 获取数据';
      }
    });

    document.body.appendChild(button);
  }

  // 显示通知消息
  function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `temu-notification ${type}`;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    // 3秒后自动移除
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 3000);
  }

  // 页面加载完成后创建浮动按钮
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', createFloatingButton);
  } else {
    createFloatingButton();
  }

  console.log('全托获取JSON插件内容脚本已加载');
})();
