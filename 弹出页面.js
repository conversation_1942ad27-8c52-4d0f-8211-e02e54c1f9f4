// 弹出页面的JavaScript逻辑
document.addEventListener('DOMContentLoaded', function() {
  const executeBtn = document.getElementById('executeRequest');
  const clearBtn = document.getElementById('clearResults');
  const statusDiv = document.getElementById('status');
  const resultsTextarea = document.getElementById('results');

  // 执行请求按钮点击事件
  executeBtn.addEventListener('click', async function() {
    try {
      // 更新状态
      updateStatus('正在执行请求...', 'loading');
      executeBtn.disabled = true;

      // 获取当前活动标签页
      const [tab] = await chrome.tabs.query({active: true, currentWindow: true});
      
      // 向内容脚本发送消息执行请求
      const response = await chrome.tabs.sendMessage(tab.id, {
        action: 'executeRequest'
      });

      if (response.success) {
        // 格式化JSON并显示结果
        const formattedJson = JSON.stringify(response.data, null, 2);
        resultsTextarea.value = formattedJson;
        updateStatus(`请求成功！获取到 ${response.data?.data?.length || 0} 条数据`, 'success');
      } else {
        resultsTextarea.value = `错误: ${response.error}`;
        updateStatus('请求失败', 'error');
      }
    } catch (error) {
      console.error('执行请求时出错:', error);
      resultsTextarea.value = `错误: ${error.message}`;
      updateStatus('请求失败', 'error');
    } finally {
      executeBtn.disabled = false;
    }
  });

  // 清空结果按钮点击事件
  clearBtn.addEventListener('click', function() {
    resultsTextarea.value = '';
    updateStatus('结果已清空', 'info');
  });

  // 更新状态显示
  function updateStatus(message, type = 'info') {
    statusDiv.textContent = message;
    statusDiv.className = `status ${type}`;
    
    // 3秒后恢复默认状态
    if (type !== 'loading') {
      setTimeout(() => {
        statusDiv.textContent = '准备就绪';
        statusDiv.className = 'status';
      }, 3000);
    }
  }

  // 初始化时检查当前页面
  chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
    const currentUrl = tabs[0].url;
    if (!currentUrl.includes('temu.com')) {
      updateStatus('建议在Temu网站上使用此插件', 'warning');
    }
  });
});
